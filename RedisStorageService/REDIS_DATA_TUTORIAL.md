# 🔍 Redis Data Exploration Tutorial

This comprehensive tutorial will guide you through exploring, analyzing, and managing Redis data locally from the `redis_data` folder.

## 📋 Table of Contents

1. [Quick Setup](#quick-setup)
2. [Understanding Redis Data Structure](#understanding-redis-data-structure)
3. [Data Exploration Tools](#data-exploration-tools)
4. [Exporting Data to CSV](#exporting-data-to-csv)
5. [Analytics and Reporting](#analytics-and-reporting)
6. [Backup and Restore](#backup-and-restore)
7. [Advanced Usage](#advanced-usage)
8. [Troubleshooting](#troubleshooting)

## 🚀 Quick Setup

### 1. Initial Setup
```bash
# Navigate to RedisStorageService directory
cd RedisStorageService

# Run the setup script
./setup_redis_tools.sh

# Activate the environment and load aliases
source redis_tools_env/bin/activate
source redis_aliases.sh
```

### 2. Verify Redis is Running
```bash
# Check if Redis service is running
redis-check

# Or manually check
docker ps | grep redis-storage
```

## 🏗️ Understanding Redis Data Structure

### Redis Data Organization

Your Redis database stores LidarLLM conversation data in the following structure:

```
Redis Database
├── request:{uuid}     - Stream request data
├── response:{uuid}    - Stream response data  
├── session:{uuid}     - User session data
└── stats:*           - Various statistics
```

### Key Patterns

- **`request:*`** - Contains prompt, context, model info, timestamps
- **`response:*`** - Contains generated responses, processing times, status
- **`session:*`** - User session tracking and conversation history
- **`stats:*`** - Storage statistics and metrics

### Data Types

Redis stores data as JSON strings containing:

**Request Data:**
```json
{
  "request_id": "uuid",
  "timestamp": "2024-01-01T12:00:00",
  "prompt": "User's question",
  "context": "LiDAR context data",
  "model_name": "llama3:8b",
  "system_prompt": "System instructions",
  "user_session": "session_uuid"
}
```

**Response Data:**
```json
{
  "request_id": "uuid",
  "timestamp": "2024-01-01T12:00:01",
  "response_text": "Complete AI response",
  "response_chunks": ["chunk1", "chunk2", ...],
  "processing_time_ms": 1500,
  "status": "completed",
  "error_message": null
}
```

## 🔍 Data Exploration Tools

### 1. Database Summary
```bash
# Get comprehensive database overview
redis-summary

# Or detailed version
python3 redis_data_explorer.py summary
```

**Output includes:**
- Total keys count
- Memory usage
- Key patterns breakdown
- Sample keys from each category

### 2. Inspect Specific Keys
```bash
# View details of a specific key
redis-key "request:12345-abcd-..."

# View response data
redis-key "response:12345-abcd-..."
```

### 3. Pattern-Based Exploration
```bash
# Export all request keys
python3 redis_data_explorer.py export --pattern "request:*"

# Export all response keys  
python3 redis_data_explorer.py export --pattern "response:*"

# Export session data
python3 redis_data_explorer.py export --pattern "session:*"
```

## 📊 Exporting Data to CSV

### 1. Export All Conversations
```bash
# Export request-response pairs as structured CSV
redis-conversations

# Or with custom filename
python3 redis_data_explorer.py conversations --output my_conversations.csv
```

**CSV Structure:**
```
request_id,timestamp,prompt,context,model_name,system_prompt,user_session,response_text,processing_time_ms,status,error_message,response_chunks_count
```

### 2. Export Raw Data
```bash
# Export all keys to CSV
redis-export

# Export specific pattern
python3 redis_data_explorer.py export --pattern "request:*" --output requests.csv
```

### 3. Custom Exports
```bash
# Export only failed requests
python3 redis_data_explorer.py export --pattern "response:*" | grep "error"

# Export recent data (last 24 hours)
# Use the analytics tool for time-based filtering
```

## 📈 Analytics and Reporting

### 1. Quick Statistics
```bash
# Get instant overview
redis-stats
```

**Shows:**
- Total conversations
- Success/failure rates
- Average processing times
- Unique sessions
- Top models used

### 2. Detailed HTML Report
```bash
# Generate comprehensive analytics report
redis-report

# Custom output file
python3 redis_analytics.py report --output my_report.html
```

**Report includes:**
- Usage patterns by hour/day
- Model performance comparison
- Error analysis
- Processing time distributions
- Session analytics

### 3. Export Analytics Data
```bash
# Export detailed analytics to CSV
redis-csv

# Custom filename
python3 redis_analytics.py csv --output analytics_data.csv
```

## 💾 Backup and Restore

### 1. Create Backups
```bash
# Create compressed backup
redis-backup

# Uncompressed backup
python3 redis_backup_restore.py backup --no-compress

# Custom filename
python3 redis_backup_restore.py backup --output my_backup.json.gz
```

### 2. List Available Backups
```bash
# Show all backup files
redis-list-backups
```

### 3. Restore from Backup
```bash
# Restore from backup (adds to existing data)
python3 redis_backup_restore.py restore backup_file.json.gz

# Clear existing data and restore
python3 redis_backup_restore.py restore backup_file.json.gz --clear
```

## 🔧 Advanced Usage

### 1. Direct Redis Commands
```bash
# Connect to Redis CLI
docker exec -it redis-storage-service redis-cli

# Common Redis commands:
KEYS *                    # List all keys
GET request:uuid          # Get specific key
SCAN 0 MATCH request:*    # Scan for pattern
INFO                      # Redis server info
DBSIZE                    # Number of keys
```

### 2. Python Integration
```python
import redis
import json

# Connect to Redis
r = redis.Redis(host='localhost', port=6379, decode_responses=True)

# Get all conversation data
request_keys = r.keys('request:*')
for key in request_keys:
    data = json.loads(r.get(key))
    print(f"Prompt: {data['prompt']}")
```

### 3. Custom Analysis Scripts
```python
# Example: Find longest conversations
import pandas as pd
from redis_analytics import RedisAnalytics

analytics = RedisAnalytics()
df = analytics.get_conversation_data()

# Find longest responses
longest_responses = df.nlargest(10, 'response_length')
print(longest_responses[['prompt', 'response_length', 'processing_time_ms']])
```

### 4. Real-time Monitoring
```bash
# Monitor Redis in real-time
docker exec -it redis-storage-service redis-cli MONITOR

# Watch key changes
docker exec -it redis-storage-service redis-cli --latency

# Memory usage monitoring
watch -n 5 'docker exec redis-storage-service redis-cli INFO memory'
```

## 🗂️ Local File System Access

### 1. Redis Data Files Location
```bash
# Redis data is stored in:
./redis-data/

# Contents:
./redis-data/appendonlydir/    # AOF (Append Only File) logs
./redis-data/dump.rdb          # RDB snapshot (if exists)
```

### 2. Understanding AOF Files
```bash
# View AOF file contents (binary format)
ls -la redis-data/appendonlydir/

# AOF files contain:
# - appendonly.aof.*.base.rdb  # Base RDB snapshot
# - appendonly.aof.*.incr.aof  # Incremental changes
# - appendonly.aof.manifest    # Manifest file
```

### 3. Manual Data Recovery
```bash
# If Redis container is down, you can:
# 1. Copy redis-data folder to backup location
# 2. Start new Redis container with same volume
# 3. Data will be automatically loaded
```

## 🛠️ Troubleshooting

### Common Issues

#### 1. Connection Refused
```bash
# Check if Redis container is running
docker ps | grep redis-storage

# Check container logs
docker logs redis-storage-service

# Restart if needed
docker restart redis-storage-service
```

#### 2. Empty Database
```bash
# Verify data exists in files
ls -la redis-data/

# Check if data was flushed
docker exec redis-storage-service redis-cli LASTSAVE
```

#### 3. Permission Issues
```bash
# Fix permissions on redis-data folder
sudo chown -R $USER:$USER redis-data/
chmod -R 755 redis-data/
```

#### 4. Python Dependencies
```bash
# Reinstall dependencies
source redis_tools_env/bin/activate
pip install --upgrade redis pandas matplotlib seaborn
```

### Performance Tips

1. **Large Datasets**: Use pattern-based exports instead of exporting all data
2. **Memory Usage**: Monitor Redis memory with `INFO memory`
3. **Backup Size**: Use compressed backups for large datasets
4. **Query Performance**: Use specific key patterns instead of `KEYS *`

## 📚 Command Reference

### Quick Commands
```bash
redis-summary          # Database overview
redis-conversations    # Export conversations to CSV
redis-stats           # Quick statistics
redis-report          # Generate HTML report
redis-backup          # Create backup
redis-check           # Check service status
```

### Detailed Commands
```bash
python3 redis_data_explorer.py summary
python3 redis_data_explorer.py export --pattern "request:*"
python3 redis_data_explorer.py conversations --output data.csv
python3 redis_data_explorer.py key "specific-key-name"

python3 redis_backup_restore.py backup --output backup.json.gz
python3 redis_backup_restore.py restore backup.json.gz
python3 redis_backup_restore.py list

python3 redis_analytics.py stats
python3 redis_analytics.py report --output report.html
python3 redis_analytics.py csv --output analytics.csv
```

## 🎯 Use Cases

### 1. Daily Monitoring
```bash
# Morning routine
redis-check && redis-stats && redis-backup
```

### 2. Weekly Analysis
```bash
# Generate weekly report
redis-report --output weekly_report_$(date +%Y%m%d).html
```

### 3. Data Export for External Analysis
```bash
# Export for Excel/PowerBI
redis-conversations --output conversations.csv
redis-csv --output analytics.csv
```

### 4. Debugging Issues
```bash
# Find failed requests
python3 redis_data_explorer.py export --pattern "response:*" | grep error
```

This tutorial provides comprehensive coverage of Redis data exploration. All tools are designed to work with your local `redis-data` folder and provide both simple commands for quick tasks and detailed options for advanced analysis.

## 🎬 Getting Started Example

Here's a complete example workflow:

```bash
# 1. Setup (one-time)
cd RedisStorageService
./setup_redis_tools.sh
source redis_tools_env/bin/activate
source redis_aliases.sh

# 2. Check status
redis-check

# 3. View summary
redis-summary

# 4. Export conversations
redis-conversations

# 5. Generate analytics report
redis-report

# 6. Create backup
redis-backup
```

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Verify Redis container is running: `docker ps | grep redis`
3. Check container logs: `docker logs redis-storage-service`
4. Ensure proper permissions on `redis-data` folder

Happy data exploring! 🚀
