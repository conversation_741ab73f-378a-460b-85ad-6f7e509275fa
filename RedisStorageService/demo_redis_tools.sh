#!/bin/bash
# demo_redis_tools.sh - Interactive demo of Redis data exploration tools

echo "🎬 Redis Data Exploration Tools Demo"
echo "===================================="

# Check if setup is complete
if [ ! -d "redis_tools_env" ]; then
    echo "❌ Tools not set up. Running setup first..."
    ./setup_redis_tools.sh
fi

# Activate environment
echo "🔄 Activating environment..."
source redis_tools_env/bin/activate
source redis_aliases.sh

echo ""
echo "🔍 Step 1: Checking Redis Service Status"
echo "----------------------------------------"
./check_redis_data.sh

echo ""
echo "📊 Step 2: Database Summary"
echo "---------------------------"
python3 redis_data_explorer.py summary

echo ""
echo "📈 Step 3: Quick Statistics"
echo "---------------------------"
python3 redis_analytics.py stats

echo ""
echo "💾 Step 4: Creating Sample Exports"
echo "-----------------------------------"

# Export conversations if data exists
echo "Exporting conversations to CSV..."
python3 redis_data_explorer.py conversations --output demo_conversations.csv

# Export all data
echo "Exporting all data to CSV..."
python3 redis_data_explorer.py export --output demo_all_data.csv

# Generate analytics report
echo "Generating analytics report..."
python3 redis_analytics.py report --output demo_analytics_report.html

# Create backup
echo "Creating backup..."
python3 redis_backup_restore.py backup --output demo_backup.json.gz

echo ""
echo "✅ Demo Complete!"
echo "=================="
echo ""
echo "📁 Generated Files:"
echo "  - demo_conversations.csv      (Structured conversation data)"
echo "  - demo_all_data.csv          (All Redis keys and values)"
echo "  - demo_analytics_report.html (Comprehensive analytics report)"
echo "  - demo_backup.json.gz        (Complete database backup)"
echo ""
echo "🔧 Available Commands:"
echo "  redis-summary      - Show database summary"
echo "  redis-conversations - Export conversations to CSV"
echo "  redis-stats        - Show quick statistics"
echo "  redis-report       - Generate HTML analytics report"
echo "  redis-backup       - Create database backup"
echo "  redis-check        - Check Redis service status"
echo ""
echo "📚 For detailed help: cat REDIS_DATA_TUTORIAL.md"
echo ""
echo "🎯 Next Steps:"
echo "1. Open demo_analytics_report.html in your browser"
echo "2. Open demo_conversations.csv in Excel/LibreOffice"
echo "3. Explore the tutorial: REDIS_DATA_TUTORIAL.md"
