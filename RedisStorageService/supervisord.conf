[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:redis]
command=redis-server /etc/redis/redis.conf
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/redis.err.log
stdout_logfile=/var/log/supervisor/redis.out.log
user=root

[program:fastapi]
command=python -m uvicorn app.main:app --host 0.0.0.0 --port 8002 --reload
directory=/app
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/fastapi.err.log
stdout_logfile=/var/log/supervisor/fastapi.out.log
environment=REDIS_HOST="localhost",REDIS_PORT="6379"
user=root
