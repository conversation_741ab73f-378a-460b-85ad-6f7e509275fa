#!/bin/bash
# check_redis_data.sh - <PERSON><PERSON><PERSON> to check Redis data locally

echo "🔍 Checking Redis Storage Service..."

# Check if Redis container is running
if docker ps | grep -q "redis-storage-service"; then
    echo "✅ Redis Storage Service container is running"
    
    # Check Redis connection
    echo "🔗 Testing Redis connection..."
    docker exec redis-storage-service redis-cli ping
    
    # Show Redis info
    echo "📊 Redis Info:"
    docker exec redis-storage-service redis-cli info server | head -10
    
    # Show stored keys
    echo "🔑 Stored Keys (first 10):"
    docker exec redis-storage-service redis-cli keys "*" | head -10
    
    # Show storage stats via API
    echo "📈 Storage Statistics:"
    curl -s http://localhost:8002/api/v1/stats | python3 -m json.tool 2>/dev/null || echo "API not responding"
    
    # Check local data directory
    echo "📁 Local Redis Data Directory:"
    ls -la ./redis-data/ 2>/dev/null || echo "No local data directory found"
    
else
    echo "❌ Redis Storage Service container is not running"
    echo "💡 Start containers with: ./start_containers.sh"
fi

echo "🏁 Redis check complete"
