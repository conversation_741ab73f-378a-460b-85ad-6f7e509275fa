# Redis Storage Service

This service provides Redis-based storage for LidarLLM stream data, capturing input and output from the stream API calls.

## Features

- Store stream request and response data
- Conversation session management
- Data persistence with TTL (Time To Live)
- Storage statistics and monitoring
- Health checks and cleanup utilities
- Local volume mapping for data persistence

## API Endpoints

### Storage Operations
- `POST /api/v1/store-request` - Store stream request data
- `POST /api/v1/store-response` - Store stream response data
- `GET /api/v1/request/{request_id}` - Retrieve request by ID
- `GET /api/v1/response/{request_id}` - Retrieve response by request ID

### Session Management
- `POST /api/v1/session` - Create new conversation session
- `PUT /api/v1/session/{session_id}/activity` - Update session activity
- `GET /api/v1/conversation/{session_id}` - Get conversation history

### Monitoring
- `GET /api/v1/stats` - Get storage statistics
- `GET /health` - Health check
- `DELETE /api/v1/cleanup` - Clean up expired data

## Environment Variables

- `REDIS_HOST` - Redis host (default: localhost)
- `REDIS_PORT` - Redis port (default: 6379)
- `REDIS_DB` - Redis database number (default: 0)
- `REDIS_PASSWORD` - Redis password (optional)
- `REDIS_TTL` - Data TTL in seconds (default: 604800 = 7 days)

## Data Models

### StreamRequestData
- `request_id` - Unique request identifier
- `timestamp` - Request timestamp
- `prompt` - User prompt
- `context` - LiDAR context data
- `model_name` - LLM model used
- `system_prompt` - System prompt used
- `user_session` - Session identifier

### StreamResponseData
- `request_id` - Associated request ID
- `timestamp` - Response timestamp
- `response_text` - Complete response text
- `response_chunks` - Individual response chunks
- `processing_time_ms` - Processing time
- `status` - Response status
- `error_message` - Error message if any

## Local Development

The Redis data is stored in a local volume mapped to `./redis-data` directory, allowing you to:
- View stored data locally
- Make changes that persist across container restarts
- Backup and restore data easily

## Usage

The service automatically captures data when integrated with the LidarInferenceService stream API.