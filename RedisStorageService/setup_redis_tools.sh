#!/bin/bash
# setup_redis_tools.sh - Setup script for Redis data exploration tools

echo "🔧 Setting up Redis Data Exploration Tools..."

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed"
    exit 1
fi

# Check if pip is available
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is required but not installed"
    exit 1
fi

# Create virtual environment if it doesn't exist
if [ ! -d "redis_tools_env" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv redis_tools_env
fi

# Activate virtual environment
echo "🔄 Activating virtual environment..."
source redis_tools_env/bin/activate

# Install required packages
echo "📥 Installing required packages..."
pip install --upgrade pip
pip install redis pandas matplotlib seaborn

# Make scripts executable
echo "🔧 Making scripts executable..."
chmod +x redis_data_explorer.py
chmod +x redis_backup_restore.py
chmod +x redis_analytics.py
chmod +x check_redis_data.sh

# Create aliases file
echo "📝 Creating aliases..."
cat > redis_aliases.sh << 'EOF'
#!/bin/bash
# Redis Tools Aliases - Source this file to use convenient aliases

# Activate the virtual environment
alias redis-env='source redis_tools_env/bin/activate'

# Data exploration
alias redis-summary='python3 redis_data_explorer.py summary'
alias redis-export='python3 redis_data_explorer.py export'
alias redis-conversations='python3 redis_data_explorer.py conversations'
alias redis-key='python3 redis_data_explorer.py key'

# Backup and restore
alias redis-backup='python3 redis_backup_restore.py backup'
alias redis-restore='python3 redis_backup_restore.py restore'
alias redis-list-backups='python3 redis_backup_restore.py list'

# Analytics
alias redis-stats='python3 redis_analytics.py stats'
alias redis-report='python3 redis_analytics.py report'
alias redis-csv='python3 redis_analytics.py csv'

# Quick checks
alias redis-check='./check_redis_data.sh'

echo "Redis tools aliases loaded! 🚀"
echo "Available commands:"
echo "  redis-summary      - Show database summary"
echo "  redis-export       - Export all data to CSV"
echo "  redis-conversations - Export conversations to CSV"
echo "  redis-backup       - Create database backup"
echo "  redis-stats        - Show quick statistics"
echo "  redis-report       - Generate HTML analytics report"
echo "  redis-check        - Check Redis service status"
EOF

chmod +x redis_aliases.sh

echo "✅ Setup complete!"
echo ""
echo "🚀 Quick Start Guide:"
echo "1. Activate the environment: source redis_tools_env/bin/activate"
echo "2. Load aliases: source redis_aliases.sh"
echo "3. Check Redis status: redis-check"
echo "4. View summary: redis-summary"
echo "5. Export data: redis-conversations"
echo ""
echo "📚 For detailed help on any tool, run:"
echo "   python3 redis_data_explorer.py --help"
echo "   python3 redis_backup_restore.py --help"
echo "   python3 redis_analytics.py --help"
