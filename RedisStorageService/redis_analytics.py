#!/usr/bin/env python3
"""
Redis Analytics Tool - Generate insights and reports from Redis data
"""

import redis
import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import argparse
import sys
from typing import Dict, List, Any
import re
from collections import Counter, defaultdict

class RedisAnalytics:
    def __init__(self, host='localhost', port=6379, db=0, password=None):
        """Initialize Redis connection"""
        try:
            self.redis_client = redis.Redis(
                host=host, 
                port=port, 
                db=db, 
                password=password,
                decode_responses=True
            )
            self.redis_client.ping()
            print(f"✅ Connected to Redis at {host}:{port}")
        except Exception as e:
            print(f"❌ Failed to connect to Redis: {e}")
            sys.exit(1)
    
    def get_conversation_data(self) -> pd.DataFrame:
        """Extract and structure conversation data"""
        request_keys = self.redis_client.keys('request:*')
        
        conversations = []
        
        for req_key in request_keys:
            request_id = req_key.split(':', 1)[1]
            response_key = f'response:{request_id}'
            
            try:
                req_data = json.loads(self.redis_client.get(req_key) or '{}')
                resp_data = json.loads(self.redis_client.get(response_key) or '{}')
                
                conversation = {
                    'request_id': request_id,
                    'timestamp': req_data.get('timestamp'),
                    'prompt': req_data.get('prompt', ''),
                    'context': req_data.get('context', ''),
                    'model_name': req_data.get('model_name'),
                    'system_prompt': req_data.get('system_prompt'),
                    'user_session': req_data.get('user_session'),
                    'response_text': resp_data.get('response_text', ''),
                    'processing_time_ms': resp_data.get('processing_time_ms'),
                    'status': resp_data.get('status'),
                    'error_message': resp_data.get('error_message'),
                    'response_chunks_count': len(resp_data.get('response_chunks', [])),
                    'prompt_length': len(req_data.get('prompt', '')),
                    'context_length': len(req_data.get('context', '')),
                    'response_length': len(resp_data.get('response_text', ''))
                }
                
                # Parse timestamp
                if conversation['timestamp']:
                    try:
                        conversation['datetime'] = pd.to_datetime(conversation['timestamp'])
                        conversation['date'] = conversation['datetime'].date()
                        conversation['hour'] = conversation['datetime'].hour
                    except:
                        conversation['datetime'] = None
                
                conversations.append(conversation)
                
            except Exception as e:
                print(f"⚠️  Error processing {req_key}: {e}")
        
        return pd.DataFrame(conversations)
    
    def generate_usage_report(self, output_file: str = None) -> str:
        """Generate comprehensive usage analytics report"""
        if not output_file:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f'redis_analytics_report_{timestamp}.html'
        
        df = self.get_conversation_data()
        
        if df.empty:
            print("❌ No conversation data found")
            return None
        
        # Generate HTML report
        html_content = self._generate_html_report(df)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"✅ Analytics report generated: {output_file}")
        return output_file
    
    def _generate_html_report(self, df: pd.DataFrame) -> str:
        """Generate HTML analytics report"""
        
        # Basic statistics
        total_conversations = len(df)
        successful_conversations = len(df[df['status'] == 'completed'])
        failed_conversations = len(df[df['status'] == 'error'])
        success_rate = (successful_conversations / total_conversations * 100) if total_conversations > 0 else 0
        
        # Model usage
        model_usage = df['model_name'].value_counts()
        
        # Processing time statistics
        processing_times = df[df['processing_time_ms'].notna()]['processing_time_ms']
        avg_processing_time = processing_times.mean() if not processing_times.empty else 0
        
        # Session statistics
        unique_sessions = df['user_session'].nunique()
        
        # Time-based analysis
        if 'datetime' in df.columns and df['datetime'].notna().any():
            df_with_time = df[df['datetime'].notna()]
            date_range = f"{df_with_time['datetime'].min().strftime('%Y-%m-%d')} to {df_with_time['datetime'].max().strftime('%Y-%m-%d')}"
            hourly_usage = df_with_time.groupby('hour').size()
            daily_usage = df_with_time.groupby('date').size()
        else:
            date_range = "No timestamp data available"
            hourly_usage = pd.Series()
            daily_usage = pd.Series()
        
        # Generate HTML
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Redis Analytics Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .metric {{ display: inline-block; margin: 10px; padding: 15px; background-color: #e8f4f8; border-radius: 5px; }}
                .metric h3 {{ margin: 0; color: #2c3e50; }}
                .metric p {{ margin: 5px 0 0 0; font-size: 24px; font-weight: bold; color: #3498db; }}
                .section {{ margin: 30px 0; }}
                .chart {{ margin: 20px 0; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🔍 Redis Analytics Report</h1>
                <p>Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p>Data Period: {date_range}</p>
            </div>
            
            <div class="section">
                <h2>📊 Key Metrics</h2>
                <div class="metric">
                    <h3>Total Conversations</h3>
                    <p>{total_conversations}</p>
                </div>
                <div class="metric">
                    <h3>Success Rate</h3>
                    <p>{success_rate:.1f}%</p>
                </div>
                <div class="metric">
                    <h3>Avg Processing Time</h3>
                    <p>{avg_processing_time:.0f}ms</p>
                </div>
                <div class="metric">
                    <h3>Unique Sessions</h3>
                    <p>{unique_sessions}</p>
                </div>
            </div>
            
            <div class="section">
                <h2>🤖 Model Usage</h2>
                <table>
                    <tr><th>Model</th><th>Usage Count</th><th>Percentage</th></tr>
        """
        
        for model, count in model_usage.items():
            percentage = (count / total_conversations * 100) if total_conversations > 0 else 0
            html += f"<tr><td>{model}</td><td>{count}</td><td>{percentage:.1f}%</td></tr>"
        
        html += """
                </table>
            </div>
            
            <div class="section">
                <h2>⏰ Usage Patterns</h2>
        """
        
        if not hourly_usage.empty:
            html += "<h3>Hourly Usage Distribution</h3><table><tr><th>Hour</th><th>Conversations</th></tr>"
            for hour, count in hourly_usage.items():
                html += f"<tr><td>{hour}:00</td><td>{count}</td></tr>"
            html += "</table>"
        
        if not daily_usage.empty:
            html += "<h3>Daily Usage (Last 10 days)</h3><table><tr><th>Date</th><th>Conversations</th></tr>"
            for date, count in daily_usage.tail(10).items():
                html += f"<tr><td>{date}</td><td>{count}</td></tr>"
            html += "</table>"
        
        # Error analysis
        if failed_conversations > 0:
            error_messages = df[df['status'] == 'error']['error_message'].value_counts()
            html += """
                <div class="section">
                    <h2>❌ Error Analysis</h2>
                    <table>
                        <tr><th>Error Message</th><th>Count</th></tr>
            """
            for error, count in error_messages.items():
                html += f"<tr><td>{error}</td><td>{count}</td></tr>"
            html += "</table></div>"
        
        # Performance analysis
        if not processing_times.empty:
            html += f"""
                <div class="section">
                    <h2>⚡ Performance Analysis</h2>
                    <table>
                        <tr><th>Metric</th><th>Value</th></tr>
                        <tr><td>Average Processing Time</td><td>{processing_times.mean():.0f}ms</td></tr>
                        <tr><td>Median Processing Time</td><td>{processing_times.median():.0f}ms</td></tr>
                        <tr><td>Min Processing Time</td><td>{processing_times.min():.0f}ms</td></tr>
                        <tr><td>Max Processing Time</td><td>{processing_times.max():.0f}ms</td></tr>
                        <tr><td>95th Percentile</td><td>{processing_times.quantile(0.95):.0f}ms</td></tr>
                    </table>
                </div>
            """
        
        html += """
            </body>
            </html>
        """
        
        return html
    
    def export_analytics_csv(self, output_file: str = None) -> str:
        """Export detailed analytics data to CSV"""
        if not output_file:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f'redis_analytics_data_{timestamp}.csv'
        
        df = self.get_conversation_data()
        
        if df.empty:
            print("❌ No conversation data found")
            return None
        
        # Add derived columns for analysis
        df['success'] = df['status'] == 'completed'
        df['has_error'] = df['status'] == 'error'
        df['prompt_words'] = df['prompt'].str.split().str.len()
        df['response_words'] = df['response_text'].str.split().str.len()
        
        # Save to CSV
        df.to_csv(output_file, index=False)
        
        print(f"✅ Analytics data exported to: {output_file}")
        print(f"   Total records: {len(df)}")
        print(f"   Columns: {len(df.columns)}")
        
        return output_file
    
    def print_quick_stats(self):
        """Print quick statistics to console"""
        df = self.get_conversation_data()
        
        if df.empty:
            print("❌ No conversation data found")
            return
        
        print("\n" + "="*50)
        print("📊 QUICK STATISTICS")
        print("="*50)
        
        print(f"Total Conversations: {len(df)}")
        print(f"Successful: {len(df[df['status'] == 'completed'])}")
        print(f"Failed: {len(df[df['status'] == 'error'])}")
        
        if 'processing_time_ms' in df.columns:
            processing_times = df[df['processing_time_ms'].notna()]['processing_time_ms']
            if not processing_times.empty:
                print(f"Avg Processing Time: {processing_times.mean():.0f}ms")
        
        print(f"Unique Sessions: {df['user_session'].nunique()}")
        
        model_usage = df['model_name'].value_counts()
        print(f"\nTop Models:")
        for model, count in model_usage.head(3).items():
            print(f"  {model}: {count} uses")
        
        print("="*50)


def main():
    parser = argparse.ArgumentParser(description='Redis Analytics Tool')
    parser.add_argument('--host', default='localhost', help='Redis host')
    parser.add_argument('--port', type=int, default=6379, help='Redis port')
    parser.add_argument('--db', type=int, default=0, help='Redis database number')
    parser.add_argument('--password', help='Redis password')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Quick stats
    subparsers.add_parser('stats', help='Show quick statistics')
    
    # Generate report
    report_parser = subparsers.add_parser('report', help='Generate HTML analytics report')
    report_parser.add_argument('--output', help='Output HTML file name')
    
    # Export CSV
    csv_parser = subparsers.add_parser('csv', help='Export analytics data to CSV')
    csv_parser.add_argument('--output', help='Output CSV file name')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Initialize analytics tool
    analytics = RedisAnalytics(
        host=args.host,
        port=args.port,
        db=args.db,
        password=args.password
    )
    
    # Execute command
    if args.command == 'stats':
        analytics.print_quick_stats()
    elif args.command == 'report':
        analytics.generate_usage_report(args.output)
    elif args.command == 'csv':
        analytics.export_analytics_csv(args.output)


if __name__ == '__main__':
    main()
