# Redis configuration for LidarLLM Storage Service

# Network
bind 0.0.0.0
port 6379
protected-mode no

# General
daemonize no
supervised no
pidfile /var/run/redis_6379.pid

# Logging
loglevel notice
logfile ""

# Persistence
save 900 1
save 300 10
save 60 10000

# Data directory - use volume mapped directory
dir /app/redis-data

# Memory management
maxmemory 2gb
maxmemory-policy allkeys-lru

# Append only file
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec

# Security (basic)
# requirepass your_password_here

# Client timeout
timeout 300

# TCP keepalive
tcp-keepalive 300

# Database
databases 16

# Slow log
slowlog-log-slower-than 10000
slowlog-max-len 128
