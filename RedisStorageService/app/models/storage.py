from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field
import uuid


class StreamRequestData(BaseModel):
    """Model for storing stream API request data"""
    request_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    prompt: str
    context: str
    model_name: Optional[str] = "llama3:8b"
    system_prompt: Optional[str] = None
    user_session: Optional[str] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class StreamResponseData(BaseModel):
    """Model for storing stream API response data"""
    request_id: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    response_text: str
    response_chunks: list[str] = Field(default_factory=list)
    processing_time_ms: Optional[float] = None
    status: str = "completed"  # completed, error, partial
    error_message: Optional[str] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ConversationSession(BaseModel):
    """Model for storing conversation sessions"""
    session_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    created_at: datetime = Field(default_factory=datetime.utcnow)
    last_activity: datetime = Field(default_factory=datetime.utcnow)
    request_count: int = 0
    user_id: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class StorageStats(BaseModel):
    """Model for storage statistics"""
    total_requests: int
    total_responses: int
    total_sessions: int
    storage_size_mb: float
    oldest_entry: Optional[datetime] = None
    newest_entry: Optional[datetime] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
