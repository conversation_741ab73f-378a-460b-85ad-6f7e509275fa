from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional, Dict, Any
import logging
from app.services.redis_service import RedisStorageService
from app.models.storage import StreamRequestData, StreamResponseData, ConversationSession, StorageStats

logger = logging.getLogger(__name__)
router = APIRouter()

def get_redis_service() -> RedisStorageService:
    """Dependency to get Redis service - will be overridden by main app"""
    pass

@router.post("/store-request", response_model=dict)
async def store_request(
    request_data: StreamRequestData,
    redis_svc: RedisStorageService = Depends(get_redis_service)
):
    """Store a stream request"""
    try:
        request_id = redis_svc.store_request(request_data)
        return {"request_id": request_id, "status": "stored"}
    except Exception as e:
        logger.error(f"Failed to store request: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/store-response", response_model=dict)
async def store_response(
    response_data: StreamResponseData,
    redis_svc: RedisStorageService = Depends(get_redis_service)
):
    """Store a stream response"""
    try:
        request_id = redis_svc.store_response(response_data)
        return {"request_id": request_id, "status": "stored"}
    except Exception as e:
        logger.error(f"Failed to store response: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/request/{request_id}", response_model=StreamRequestData)
async def get_request(
    request_id: str,
    redis_svc: RedisStorageService = Depends(get_redis_service)
):
    """Get a stored request by ID"""
    try:
        request_data = redis_svc.get_request(request_id)
        if not request_data:
            raise HTTPException(status_code=404, detail="Request not found")
        return request_data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get request: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/response/{request_id}", response_model=StreamResponseData)
async def get_response(
    request_id: str,
    redis_svc: RedisStorageService = Depends(get_redis_service)
):
    """Get a stored response by request ID"""
    try:
        response_data = redis_svc.get_response(request_id)
        if not response_data:
            raise HTTPException(status_code=404, detail="Response not found")
        return response_data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get response: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/conversation/{session_id}")
async def get_conversation_history(
    session_id: str,
    limit: int = Query(50, ge=1, le=100),
    redis_svc: RedisStorageService = Depends(get_redis_service)
):
    """Get conversation history for a session"""
    try:
        history = redis_svc.get_conversation_history(session_id, limit)
        return {"session_id": session_id, "history": history, "count": len(history)}
    except Exception as e:
        logger.error(f"Failed to get conversation history: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/session", response_model=ConversationSession)
async def create_session(
    user_id: Optional[str] = None,
    redis_svc: RedisStorageService = Depends(get_redis_service)
):
    """Create a new conversation session"""
    try:
        session = redis_svc.create_session(user_id)
        return session
    except Exception as e:
        logger.error(f"Failed to create session: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/session/{session_id}/activity")
async def update_session_activity(
    session_id: str,
    redis_svc: RedisStorageService = Depends(get_redis_service)
):
    """Update session activity"""
    try:
        redis_svc.update_session_activity(session_id)
        return {"session_id": session_id, "status": "updated"}
    except Exception as e:
        logger.error(f"Failed to update session activity: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/stats", response_model=StorageStats)
async def get_storage_stats(
    redis_svc: RedisStorageService = Depends(get_redis_service)
):
    """Get storage statistics"""
    try:
        stats = redis_svc.get_storage_stats()
        return stats
    except Exception as e:
        logger.error(f"Failed to get storage stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/cleanup")
async def cleanup_expired_data(
    days_old: int = Query(7, ge=1, le=365),
    redis_svc: RedisStorageService = Depends(get_redis_service)
):
    """Clean up expired data"""
    try:
        deleted_count = redis_svc.cleanup_expired_data(days_old)
        return {"deleted_count": deleted_count, "days_old": days_old}
    except Exception as e:
        logger.error(f"Failed to cleanup data: {e}")
        raise HTTPException(status_code=500, detail=str(e))
