import json
import redis
import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from app.models.storage import StreamRequestData, StreamResponseData, ConversationSession, StorageStats
import os

logger = logging.getLogger(__name__)


class RedisStorageService:
    """Redis-based storage service for LidarLLM stream data"""
    
    def __init__(self):
        self.redis_host = os.getenv("REDIS_HOST", "localhost")
        self.redis_port = int(os.getenv("REDIS_PORT", "6379"))
        self.redis_db = int(os.getenv("REDIS_DB", "0"))
        self.redis_password = os.getenv("REDIS_PASSWORD", None)
        
        # Redis key prefixes
        self.REQUEST_PREFIX = "lidar:request:"
        self.RESPONSE_PREFIX = "lidar:response:"
        self.SESSION_PREFIX = "lidar:session:"
        self.STATS_KEY = "lidar:stats"
        
        # TTL settings (in seconds)
        self.DEFAULT_TTL = int(os.getenv("REDIS_TTL", "604800"))  # 7 days
        
        self._connect()
    
    def _connect(self):
        """Initialize Redis connection"""
        try:
            self.redis_client = redis.Redis(
                host=self.redis_host,
                port=self.redis_port,
                db=self.redis_db,
                password=self.redis_password,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True
            )
            # Test connection
            self.redis_client.ping()
            logger.info(f"Connected to Redis at {self.redis_host}:{self.redis_port}")
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise
    
    def store_request(self, request_data: StreamRequestData) -> str:
        """Store stream request data"""
        try:
            key = f"{self.REQUEST_PREFIX}{request_data.request_id}"
            data = request_data.model_dump_json()
            
            # Store with TTL
            self.redis_client.setex(key, self.DEFAULT_TTL, data)
            
            # Update stats
            self._update_stats("requests", 1)
            
            logger.info(f"Stored request {request_data.request_id}")
            return request_data.request_id
            
        except Exception as e:
            logger.error(f"Failed to store request: {e}")
            raise
    
    def store_response(self, response_data: StreamResponseData) -> str:
        """Store stream response data"""
        try:
            key = f"{self.RESPONSE_PREFIX}{response_data.request_id}"
            data = response_data.model_dump_json()
            
            # Store with TTL
            self.redis_client.setex(key, self.DEFAULT_TTL, data)
            
            # Update stats
            self._update_stats("responses", 1)
            
            logger.info(f"Stored response for request {response_data.request_id}")
            return response_data.request_id
            
        except Exception as e:
            logger.error(f"Failed to store response: {e}")
            raise
    
    def get_request(self, request_id: str) -> Optional[StreamRequestData]:
        """Retrieve request data by ID"""
        try:
            key = f"{self.REQUEST_PREFIX}{request_id}"
            data = self.redis_client.get(key)
            
            if data:
                return StreamRequestData.model_validate_json(data)
            return None
            
        except Exception as e:
            logger.error(f"Failed to retrieve request {request_id}: {e}")
            return None
    
    def get_response(self, request_id: str) -> Optional[StreamResponseData]:
        """Retrieve response data by request ID"""
        try:
            key = f"{self.RESPONSE_PREFIX}{request_id}"
            data = self.redis_client.get(key)
            
            if data:
                return StreamResponseData.model_validate_json(data)
            return None
            
        except Exception as e:
            logger.error(f"Failed to retrieve response for {request_id}: {e}")
            return None

    def get_conversation_history(self, session_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get conversation history for a session"""
        try:
            # Get all request keys for this session
            pattern = f"{self.REQUEST_PREFIX}*"
            keys = self.redis_client.keys(pattern)

            conversations = []
            for key in keys:
                data = self.redis_client.get(key)
                if data:
                    request = StreamRequestData.model_validate_json(data)
                    if request.user_session == session_id:
                        # Get corresponding response
                        response = self.get_response(request.request_id)
                        conversations.append({
                            "request": request.model_dump(),
                            "response": response.model_dump() if response else None
                        })

            # Sort by timestamp and limit
            conversations.sort(key=lambda x: x["request"]["timestamp"], reverse=True)
            return conversations[:limit]

        except Exception as e:
            logger.error(f"Failed to get conversation history: {e}")
            return []

    def create_session(self, user_id: Optional[str] = None) -> ConversationSession:
        """Create a new conversation session"""
        try:
            session = ConversationSession(user_id=user_id)
            key = f"{self.SESSION_PREFIX}{session.session_id}"
            data = session.model_dump_json()

            # Store with TTL
            self.redis_client.setex(key, self.DEFAULT_TTL, data)

            # Update stats
            self._update_stats("sessions", 1)

            logger.info(f"Created session {session.session_id}")
            return session

        except Exception as e:
            logger.error(f"Failed to create session: {e}")
            raise

    def update_session_activity(self, session_id: str):
        """Update session last activity timestamp"""
        try:
            key = f"{self.SESSION_PREFIX}{session_id}"
            data = self.redis_client.get(key)

            if data:
                session = ConversationSession.model_validate_json(data)
                session.last_activity = datetime.utcnow()
                session.request_count += 1

                # Update in Redis
                self.redis_client.setex(key, self.DEFAULT_TTL, session.model_dump_json())

        except Exception as e:
            logger.error(f"Failed to update session activity: {e}")

    def get_storage_stats(self) -> StorageStats:
        """Get storage statistics"""
        try:
            # Count keys by pattern
            request_keys = self.redis_client.keys(f"{self.REQUEST_PREFIX}*")
            response_keys = self.redis_client.keys(f"{self.RESPONSE_PREFIX}*")
            session_keys = self.redis_client.keys(f"{self.SESSION_PREFIX}*")

            # Get memory usage (approximate)
            info = self.redis_client.info('memory')
            memory_mb = info.get('used_memory', 0) / (1024 * 1024)

            # Get oldest and newest entries
            oldest_entry = None
            newest_entry = None

            if request_keys:
                timestamps = []
                for key in request_keys[:10]:  # Sample first 10 for performance
                    data = self.redis_client.get(key)
                    if data:
                        request = StreamRequestData.model_validate_json(data)
                        timestamps.append(request.timestamp)

                if timestamps:
                    oldest_entry = min(timestamps)
                    newest_entry = max(timestamps)

            return StorageStats(
                total_requests=len(request_keys),
                total_responses=len(response_keys),
                total_sessions=len(session_keys),
                storage_size_mb=round(memory_mb, 2),
                oldest_entry=oldest_entry,
                newest_entry=newest_entry
            )

        except Exception as e:
            logger.error(f"Failed to get storage stats: {e}")
            return StorageStats(
                total_requests=0,
                total_responses=0,
                total_sessions=0,
                storage_size_mb=0.0
            )

    def cleanup_expired_data(self, days_old: int = 7):
        """Clean up data older than specified days"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_old)

            # Clean up requests
            request_keys = self.redis_client.keys(f"{self.REQUEST_PREFIX}*")
            deleted_count = 0

            for key in request_keys:
                data = self.redis_client.get(key)
                if data:
                    request = StreamRequestData.model_validate_json(data)
                    if request.timestamp < cutoff_date:
                        self.redis_client.delete(key)
                        # Also delete corresponding response
                        response_key = f"{self.RESPONSE_PREFIX}{request.request_id}"
                        self.redis_client.delete(response_key)
                        deleted_count += 1

            logger.info(f"Cleaned up {deleted_count} expired entries")
            return deleted_count

        except Exception as e:
            logger.error(f"Failed to cleanup expired data: {e}")
            return 0

    def _update_stats(self, stat_type: str, increment: int = 1):
        """Update statistics counters"""
        try:
            key = f"{self.STATS_KEY}:{stat_type}"
            self.redis_client.incr(key, increment)
        except Exception as e:
            logger.error(f"Failed to update stats: {e}")

    def health_check(self) -> Dict[str, Any]:
        """Perform health check on Redis connection"""
        try:
            # Test basic operations
            test_key = "health_check_test"
            self.redis_client.set(test_key, "test", ex=10)
            value = self.redis_client.get(test_key)
            self.redis_client.delete(test_key)

            # Get Redis info
            info = self.redis_client.info()

            return {
                "status": "healthy",
                "redis_version": info.get("redis_version"),
                "connected_clients": info.get("connected_clients"),
                "used_memory_human": info.get("used_memory_human"),
                "uptime_in_seconds": info.get("uptime_in_seconds"),
                "test_operation": "success" if value == "test" else "failed"
            }

        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }
