from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import logging
from app.services.redis_service import RedisStorageService
from app.models.storage import StreamRequestData, StreamResponseData, ConversationSession, StorageStats
from app.api.v1 import storage
import uvicorn

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global Redis service instance
redis_service = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global redis_service
    
    # Startup
    logger.info("Starting Redis Storage Service...")
    try:
        redis_service = RedisStorageService()
        logger.info("Redis Storage Service started successfully")
    except Exception as e:
        logger.error(f"Failed to start Redis Storage Service: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down Redis Storage Service...")

# Create FastAPI app
app = FastAPI(
    title="Redis Storage Service",
    description="Storage service for LidarLLM stream data using Redis",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Dependency to get Redis service
def get_redis_service() -> RedisStorageService:
    if redis_service is None:
        raise HTTPException(status_code=503, detail="Redis service not available")
    return redis_service

# Include API routes
app.include_router(storage.router, prefix="/api/v1", tags=["storage"])

@app.get("/health")
async def health_check(redis_svc: RedisStorageService = Depends(get_redis_service)):
    """Health check endpoint"""
    try:
        health_status = redis_svc.health_check()
        return {
            "service": "Redis Storage Service",
            "status": "healthy",
            "redis": health_status
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail=f"Service unhealthy: {e}")

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "Redis Storage Service",
        "version": "1.0.0",
        "description": "Storage service for LidarLLM stream data"
    }

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8002,
        reload=True,
        log_level="info"
    )
