# 🚀 Redis Data Tools - Quick Reference

## 🔧 One-Time Setup
```bash
cd RedisStorageService
./setup_redis_tools.sh
source redis_tools_env/bin/activate
source redis_aliases.sh
```

## 📊 Essential Commands

### Database Overview
```bash
redis-summary          # Complete database summary
redis-stats           # Quick statistics
redis-check           # Service status
```

### Data Export
```bash
redis-conversations    # Export conversations to CSV
redis-export          # Export all data to CSV
redis-csv             # Export analytics data to CSV
```

### Analytics & Reporting
```bash
redis-report          # Generate HTML analytics report
python3 redis_analytics.py stats    # Console statistics
```

### Backup & Restore
```bash
redis-backup          # Create compressed backup
redis-list-backups    # List available backups
python3 redis_backup_restore.py restore backup.json.gz
```

## 🔍 Data Structure

### Key Patterns
- `request:*` - User prompts and context
- `response:*` - AI responses and metrics
- `session:*` - User session data
- `stats:*` - System statistics

### CSV Export Columns
**Conversations CSV:**
- request_id, timestamp, prompt, context
- model_name, system_prompt, user_session
- response_text, processing_time_ms, status

## 📁 File Locations
- **Redis Data**: `./redis-data/`
- **Exports**: Current directory (`.csv` files)
- **Reports**: Current directory (`.html` files)
- **Backups**: Current directory (`.json.gz` files)

## 🛠️ Troubleshooting
```bash
# Check Redis container
docker ps | grep redis-storage

# Check container logs
docker logs redis-storage-service

# Restart Redis service
docker restart redis-storage-service

# Fix permissions
sudo chown -R $USER:$USER redis-data/
```

## 🎯 Common Workflows

### Daily Monitoring
```bash
redis-check && redis-stats
```

### Weekly Analysis
```bash
redis-report --output weekly_$(date +%Y%m%d).html
redis-conversations --output weekly_data.csv
```

### Data Backup
```bash
redis-backup --output backup_$(date +%Y%m%d).json.gz
```

### Export for External Analysis
```bash
redis-conversations  # For Excel/PowerBI
redis-csv           # For detailed analytics
```

## 📞 Quick Help
- **Full Tutorial**: `cat REDIS_DATA_TUTORIAL.md`
- **Tool Help**: `python3 redis_data_explorer.py --help`
- **Demo**: `./demo_redis_tools.sh`
