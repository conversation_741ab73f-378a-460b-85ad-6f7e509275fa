#!/usr/bin/env python3
"""
Redis Data Explorer - Comprehensive tool for exploring and exporting Redis data
"""

import redis
import json
import csv
import pandas as pd
from datetime import datetime
import argparse
import os
import sys
from typing import Dict, List, Any, Optional
import re

class RedisDataExplorer:
    def __init__(self, host='localhost', port=6379, db=0, password=None):
        """Initialize Redis connection"""
        try:
            self.redis_client = redis.Redis(
                host=host, 
                port=port, 
                db=db, 
                password=password,
                decode_responses=True
            )
            # Test connection
            self.redis_client.ping()
            print(f"✅ Connected to Redis at {host}:{port}")
        except Exception as e:
            print(f"❌ Failed to connect to Redis: {e}")
            sys.exit(1)
    
    def get_database_info(self) -> Dict[str, Any]:
        """Get comprehensive database information"""
        info = self.redis_client.info()
        
        db_info = {
            'redis_version': info.get('redis_version'),
            'total_keys': self.redis_client.dbsize(),
            'memory_usage': info.get('used_memory_human'),
            'connected_clients': info.get('connected_clients'),
            'uptime_days': info.get('uptime_in_days'),
            'keyspace_info': {}
        }
        
        # Get keyspace information
        for key, value in info.items():
            if key.startswith('db'):
                db_info['keyspace_info'][key] = value
        
        return db_info
    
    def analyze_key_patterns(self) -> Dict[str, List[str]]:
        """Analyze and categorize keys by patterns"""
        all_keys = self.redis_client.keys('*')
        patterns = {
            'requests': [],
            'responses': [],
            'sessions': [],
            'stats': [],
            'other': []
        }
        
        for key in all_keys:
            if key.startswith('request:'):
                patterns['requests'].append(key)
            elif key.startswith('response:'):
                patterns['responses'].append(key)
            elif key.startswith('session:'):
                patterns['sessions'].append(key)
            elif key.startswith('stats:'):
                patterns['stats'].append(key)
            else:
                patterns['other'].append(key)
        
        return patterns
    
    def get_key_details(self, key: str) -> Dict[str, Any]:
        """Get detailed information about a specific key"""
        if not self.redis_client.exists(key):
            return {'error': 'Key does not exist'}
        
        key_type = self.redis_client.type(key)
        ttl = self.redis_client.ttl(key)
        
        details = {
            'key': key,
            'type': key_type,
            'ttl': ttl,
            'ttl_human': f"{ttl} seconds" if ttl > 0 else "No expiration" if ttl == -1 else "Expired",
            'memory_usage': self.redis_client.memory_usage(key) if hasattr(self.redis_client, 'memory_usage') else 'N/A'
        }
        
        # Get value based on type
        try:
            if key_type == 'string':
                value = self.redis_client.get(key)
                try:
                    # Try to parse as JSON
                    details['value'] = json.loads(value)
                    details['value_type'] = 'json'
                except:
                    details['value'] = value
                    details['value_type'] = 'string'
            elif key_type == 'hash':
                details['value'] = self.redis_client.hgetall(key)
                details['value_type'] = 'hash'
            elif key_type == 'list':
                details['value'] = self.redis_client.lrange(key, 0, -1)
                details['value_type'] = 'list'
            elif key_type == 'set':
                details['value'] = list(self.redis_client.smembers(key))
                details['value_type'] = 'set'
            elif key_type == 'zset':
                details['value'] = self.redis_client.zrange(key, 0, -1, withscores=True)
                details['value_type'] = 'sorted_set'
        except Exception as e:
            details['value'] = f"Error reading value: {e}"
            details['value_type'] = 'error'
        
        return details
    
    def export_to_csv(self, pattern: str = '*', output_file: str = None) -> str:
        """Export Redis data to CSV format"""
        if not output_file:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f'redis_export_{timestamp}.csv'
        
        keys = self.redis_client.keys(pattern)
        
        # Prepare data for CSV
        csv_data = []
        
        for key in keys:
            details = self.get_key_details(key)
            
            row = {
                'key': key,
                'type': details.get('type'),
                'ttl': details.get('ttl'),
                'value_type': details.get('value_type'),
                'value': json.dumps(details.get('value')) if isinstance(details.get('value'), (dict, list)) else str(details.get('value'))
            }
            csv_data.append(row)
        
        # Write to CSV
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['key', 'type', 'ttl', 'value_type', 'value']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(csv_data)
        
        print(f"✅ Exported {len(csv_data)} keys to {output_file}")
        return output_file
    
    def export_requests_responses_csv(self, output_file: str = None) -> str:
        """Export request-response pairs to a structured CSV"""
        if not output_file:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f'redis_conversations_{timestamp}.csv'
        
        request_keys = self.redis_client.keys('request:*')
        
        csv_data = []
        
        for req_key in request_keys:
            request_id = req_key.split(':', 1)[1]
            response_key = f'response:{request_id}'
            
            req_details = self.get_key_details(req_key)
            resp_details = self.get_key_details(response_key)
            
            req_data = req_details.get('value', {})
            resp_data = resp_details.get('value', {})
            
            if isinstance(req_data, dict) and isinstance(resp_data, dict):
                row = {
                    'request_id': request_id,
                    'timestamp': req_data.get('timestamp'),
                    'prompt': req_data.get('prompt'),
                    'context': req_data.get('context'),
                    'model_name': req_data.get('model_name'),
                    'system_prompt': req_data.get('system_prompt'),
                    'user_session': req_data.get('user_session'),
                    'response_text': resp_data.get('response_text'),
                    'processing_time_ms': resp_data.get('processing_time_ms'),
                    'status': resp_data.get('status'),
                    'error_message': resp_data.get('error_message'),
                    'response_chunks_count': len(resp_data.get('response_chunks', []))
                }
                csv_data.append(row)
        
        # Write to CSV
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'request_id', 'timestamp', 'prompt', 'context', 'model_name', 
                'system_prompt', 'user_session', 'response_text', 
                'processing_time_ms', 'status', 'error_message', 'response_chunks_count'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(csv_data)
        
        print(f"✅ Exported {len(csv_data)} conversation pairs to {output_file}")
        return output_file
    
    def print_summary(self):
        """Print a comprehensive summary of the Redis database"""
        print("\n" + "="*60)
        print("🔍 REDIS DATABASE SUMMARY")
        print("="*60)
        
        # Database info
        db_info = self.get_database_info()
        print(f"📊 Redis Version: {db_info['redis_version']}")
        print(f"🔑 Total Keys: {db_info['total_keys']}")
        print(f"💾 Memory Usage: {db_info['memory_usage']}")
        print(f"👥 Connected Clients: {db_info['connected_clients']}")
        print(f"⏰ Uptime: {db_info['uptime_days']} days")
        
        # Key patterns
        patterns = self.analyze_key_patterns()
        print(f"\n📋 KEY PATTERNS:")
        for pattern, keys in patterns.items():
            if keys:
                print(f"  {pattern.upper()}: {len(keys)} keys")
                if len(keys) <= 5:
                    for key in keys:
                        print(f"    - {key}")
                else:
                    for key in keys[:3]:
                        print(f"    - {key}")
                    print(f"    ... and {len(keys)-3} more")
        
        print("\n" + "="*60)


def main():
    parser = argparse.ArgumentParser(description='Redis Data Explorer')
    parser.add_argument('--host', default='localhost', help='Redis host')
    parser.add_argument('--port', type=int, default=6379, help='Redis port')
    parser.add_argument('--db', type=int, default=0, help='Redis database number')
    parser.add_argument('--password', help='Redis password')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Summary command
    subparsers.add_parser('summary', help='Show database summary')
    
    # Export commands
    export_parser = subparsers.add_parser('export', help='Export data to CSV')
    export_parser.add_argument('--pattern', default='*', help='Key pattern to export')
    export_parser.add_argument('--output', help='Output file name')
    
    # Export conversations
    conv_parser = subparsers.add_parser('conversations', help='Export request-response pairs')
    conv_parser.add_argument('--output', help='Output file name')
    
    # Key details
    key_parser = subparsers.add_parser('key', help='Get details about a specific key')
    key_parser.add_argument('key_name', help='Key name to inspect')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Initialize explorer
    explorer = RedisDataExplorer(
        host=args.host,
        port=args.port,
        db=args.db,
        password=args.password
    )
    
    # Execute command
    if args.command == 'summary':
        explorer.print_summary()
    elif args.command == 'export':
        explorer.export_to_csv(args.pattern, args.output)
    elif args.command == 'conversations':
        explorer.export_requests_responses_csv(args.output)
    elif args.command == 'key':
        details = explorer.get_key_details(args.key_name)
        print(json.dumps(details, indent=2))


if __name__ == '__main__':
    main()
