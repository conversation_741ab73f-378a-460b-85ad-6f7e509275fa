#!/usr/bin/env python3
"""
Redis Backup and Restore Tool
"""

import redis
import json
import os
import gzip
import argparse
from datetime import datetime
from typing import Dict, Any, List
import sys

class RedisBackupRestore:
    def __init__(self, host='localhost', port=6379, db=0, password=None):
        """Initialize Redis connection"""
        try:
            self.redis_client = redis.Redis(
                host=host, 
                port=port, 
                db=db, 
                password=password,
                decode_responses=True
            )
            self.redis_client.ping()
            print(f"✅ Connected to Redis at {host}:{port}")
        except Exception as e:
            print(f"❌ Failed to connect to Redis: {e}")
            sys.exit(1)
    
    def backup_database(self, output_file: str = None, compress: bool = True) -> str:
        """Create a complete backup of the Redis database"""
        if not output_file:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            extension = '.json.gz' if compress else '.json'
            output_file = f'redis_backup_{timestamp}{extension}'
        
        print(f"🔄 Creating backup...")
        
        # Get all keys
        all_keys = self.redis_client.keys('*')
        backup_data = {
            'metadata': {
                'backup_time': datetime.now().isoformat(),
                'total_keys': len(all_keys),
                'redis_info': self.redis_client.info()
            },
            'data': {}
        }
        
        # Backup each key
        for i, key in enumerate(all_keys):
            if i % 100 == 0:
                print(f"  Progress: {i}/{len(all_keys)} keys")
            
            key_type = self.redis_client.type(key)
            ttl = self.redis_client.ttl(key)
            
            key_data = {
                'type': key_type,
                'ttl': ttl
            }
            
            # Get value based on type
            try:
                if key_type == 'string':
                    value = self.redis_client.get(key)
                    try:
                        key_data['value'] = json.loads(value)
                    except:
                        key_data['value'] = value
                elif key_type == 'hash':
                    key_data['value'] = self.redis_client.hgetall(key)
                elif key_type == 'list':
                    key_data['value'] = self.redis_client.lrange(key, 0, -1)
                elif key_type == 'set':
                    key_data['value'] = list(self.redis_client.smembers(key))
                elif key_type == 'zset':
                    key_data['value'] = self.redis_client.zrange(key, 0, -1, withscores=True)
            except Exception as e:
                key_data['error'] = str(e)
            
            backup_data['data'][key] = key_data
        
        # Write backup file
        json_data = json.dumps(backup_data, indent=2, ensure_ascii=False)
        
        if compress:
            with gzip.open(output_file, 'wt', encoding='utf-8') as f:
                f.write(json_data)
        else:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(json_data)
        
        file_size = os.path.getsize(output_file)
        print(f"✅ Backup completed: {output_file}")
        print(f"   Keys backed up: {len(all_keys)}")
        print(f"   File size: {file_size / 1024:.1f} KB")
        
        return output_file
    
    def restore_database(self, backup_file: str, clear_existing: bool = False) -> int:
        """Restore Redis database from backup file"""
        if not os.path.exists(backup_file):
            print(f"❌ Backup file not found: {backup_file}")
            return 0
        
        print(f"🔄 Restoring from backup: {backup_file}")
        
        # Read backup file
        try:
            if backup_file.endswith('.gz'):
                with gzip.open(backup_file, 'rt', encoding='utf-8') as f:
                    backup_data = json.load(f)
            else:
                with open(backup_file, 'r', encoding='utf-8') as f:
                    backup_data = json.load(f)
        except Exception as e:
            print(f"❌ Failed to read backup file: {e}")
            return 0
        
        # Clear existing data if requested
        if clear_existing:
            print("🗑️  Clearing existing data...")
            self.redis_client.flushdb()
        
        # Restore data
        data = backup_data.get('data', {})
        restored_count = 0
        
        for key, key_data in data.items():
            try:
                key_type = key_data.get('type')
                value = key_data.get('value')
                ttl = key_data.get('ttl', -1)
                
                if 'error' in key_data:
                    print(f"⚠️  Skipping key {key}: {key_data['error']}")
                    continue
                
                # Restore based on type
                if key_type == 'string':
                    if isinstance(value, (dict, list)):
                        self.redis_client.set(key, json.dumps(value))
                    else:
                        self.redis_client.set(key, value)
                elif key_type == 'hash':
                    self.redis_client.hset(key, mapping=value)
                elif key_type == 'list':
                    if value:
                        self.redis_client.lpush(key, *reversed(value))
                elif key_type == 'set':
                    if value:
                        self.redis_client.sadd(key, *value)
                elif key_type == 'zset':
                    if value:
                        for item in value:
                            if isinstance(item, (list, tuple)) and len(item) == 2:
                                self.redis_client.zadd(key, {item[0]: item[1]})
                
                # Set TTL if specified
                if ttl > 0:
                    self.redis_client.expire(key, ttl)
                
                restored_count += 1
                
                if restored_count % 100 == 0:
                    print(f"  Progress: {restored_count}/{len(data)} keys")
                    
            except Exception as e:
                print(f"⚠️  Failed to restore key {key}: {e}")
        
        print(f"✅ Restore completed: {restored_count}/{len(data)} keys restored")
        return restored_count
    
    def list_backups(self, backup_dir: str = '.') -> List[Dict[str, Any]]:
        """List available backup files"""
        backup_files = []
        
        for filename in os.listdir(backup_dir):
            if filename.startswith('redis_backup_') and (filename.endswith('.json') or filename.endswith('.json.gz')):
                filepath = os.path.join(backup_dir, filename)
                file_stat = os.stat(filepath)
                
                backup_info = {
                    'filename': filename,
                    'filepath': filepath,
                    'size_kb': file_stat.st_size / 1024,
                    'created': datetime.fromtimestamp(file_stat.st_mtime).isoformat()
                }
                
                # Try to read metadata
                try:
                    if filename.endswith('.gz'):
                        with gzip.open(filepath, 'rt', encoding='utf-8') as f:
                            data = json.load(f)
                    else:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                    
                    metadata = data.get('metadata', {})
                    backup_info.update({
                        'backup_time': metadata.get('backup_time'),
                        'total_keys': metadata.get('total_keys', 0)
                    })
                except:
                    pass
                
                backup_files.append(backup_info)
        
        # Sort by creation time (newest first)
        backup_files.sort(key=lambda x: x['created'], reverse=True)
        return backup_files


def main():
    parser = argparse.ArgumentParser(description='Redis Backup and Restore Tool')
    parser.add_argument('--host', default='localhost', help='Redis host')
    parser.add_argument('--port', type=int, default=6379, help='Redis port')
    parser.add_argument('--db', type=int, default=0, help='Redis database number')
    parser.add_argument('--password', help='Redis password')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Backup command
    backup_parser = subparsers.add_parser('backup', help='Create database backup')
    backup_parser.add_argument('--output', help='Output file name')
    backup_parser.add_argument('--no-compress', action='store_true', help='Disable compression')
    
    # Restore command
    restore_parser = subparsers.add_parser('restore', help='Restore from backup')
    restore_parser.add_argument('backup_file', help='Backup file to restore from')
    restore_parser.add_argument('--clear', action='store_true', help='Clear existing data before restore')
    
    # List backups
    subparsers.add_parser('list', help='List available backup files')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Initialize backup tool
    backup_tool = RedisBackupRestore(
        host=args.host,
        port=args.port,
        db=args.db,
        password=args.password
    )
    
    # Execute command
    if args.command == 'backup':
        backup_tool.backup_database(
            output_file=args.output,
            compress=not args.no_compress
        )
    elif args.command == 'restore':
        backup_tool.restore_database(
            backup_file=args.backup_file,
            clear_existing=args.clear
        )
    elif args.command == 'list':
        backups = backup_tool.list_backups()
        if backups:
            print("\n📁 Available Backup Files:")
            print("-" * 80)
            for backup in backups:
                print(f"File: {backup['filename']}")
                print(f"  Size: {backup['size_kb']:.1f} KB")
                print(f"  Created: {backup['created']}")
                if 'total_keys' in backup:
                    print(f"  Keys: {backup['total_keys']}")
                print()
        else:
            print("No backup files found.")


if __name__ == '__main__':
    main()
