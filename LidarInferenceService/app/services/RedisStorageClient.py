import requests
import json
import logging
from datetime import datetime
from typing import Optional, Dict, Any
import os
import uuid

logger = logging.getLogger(__name__)


class RedisStorageClient:
    """Client for communicating with Redis Storage Service"""
    
    def __init__(self):
        self.base_url = os.getenv("REDIS_STORAGE_SERVICE_URL", "http://redis-storage:8002")
        self.enabled = os.getenv("REDIS_STORAGE_ENABLED", "true").lower() == "true"
        self.timeout = 5  # seconds
        
        if self.enabled:
            logger.info(f"Redis Storage Client initialized with URL: {self.base_url}")
        else:
            logger.info("Redis Storage Client disabled")
    
    def store_request(self, prompt: str, context: str, model_name: str = None, 
                     system_prompt: str = None, user_session: str = None) -> Optional[str]:
        """Store stream request data"""
        if not self.enabled:
            return None
            
        try:
            request_id = str(uuid.uuid4())
            payload = {
                "request_id": request_id,
                "timestamp": datetime.utcnow().isoformat(),
                "prompt": prompt,
                "context": context,
                "model_name": model_name or "llama3:8b",
                "system_prompt": system_prompt,
                "user_session": user_session
            }
            
            response = requests.post(
                f"{self.base_url}/api/v1/store-request",
                json=payload,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                logger.info(f"Stored request {request_id}")
                return request_id
            else:
                logger.warning(f"Failed to store request: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error storing request: {e}")
            return None
    
    def store_response(self, request_id: str, response_text: str, response_chunks: list = None,
                      processing_time_ms: float = None, status: str = "completed",
                      error_message: str = None) -> bool:
        """Store stream response data"""
        if not self.enabled or not request_id:
            return False
            
        try:
            payload = {
                "request_id": request_id,
                "timestamp": datetime.utcnow().isoformat(),
                "response_text": response_text,
                "response_chunks": response_chunks or [],
                "processing_time_ms": processing_time_ms,
                "status": status,
                "error_message": error_message
            }
            
            response = requests.post(
                f"{self.base_url}/api/v1/store-response",
                json=payload,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                logger.info(f"Stored response for request {request_id}")
                return True
            else:
                logger.warning(f"Failed to store response: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error storing response: {e}")
            return False
    
    def create_session(self, user_id: str = None) -> Optional[str]:
        """Create a new conversation session"""
        if not self.enabled:
            return None
            
        try:
            payload = {"user_id": user_id} if user_id else {}
            
            response = requests.post(
                f"{self.base_url}/api/v1/session",
                json=payload,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                session_data = response.json()
                session_id = session_data.get("session_id")
                logger.info(f"Created session {session_id}")
                return session_id
            else:
                logger.warning(f"Failed to create session: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating session: {e}")
            return None
    
    def update_session_activity(self, session_id: str) -> bool:
        """Update session activity"""
        if not self.enabled or not session_id:
            return False
            
        try:
            response = requests.put(
                f"{self.base_url}/api/v1/session/{session_id}/activity",
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                return True
            else:
                logger.warning(f"Failed to update session activity: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error updating session activity: {e}")
            return False
    
    def get_storage_stats(self) -> Optional[Dict[str, Any]]:
        """Get storage statistics"""
        if not self.enabled:
            return None
            
        try:
            response = requests.get(
                f"{self.base_url}/api/v1/stats",
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.warning(f"Failed to get storage stats: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting storage stats: {e}")
            return None
    
    def health_check(self) -> Dict[str, Any]:
        """Check Redis storage service health"""
        try:
            response = requests.get(
                f"{self.base_url}/health",
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                return {"status": "healthy", "details": response.json()}
            else:
                return {"status": "unhealthy", "error": f"HTTP {response.status_code}"}
                
        except Exception as e:
            return {"status": "unhealthy", "error": str(e)}
